#!/usr/bin/env python3
"""
Test script to verify ZED camera connection and fix.
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import pyzed.sl as sl
    print("ZED SDK imported successfully")
    
    # Test ZED camera discovery
    print("Discovering ZED cameras...")
    zed_list = sl.Camera.get_device_list()
    print(f"Found {len(zed_list)} ZED cameras")
    
    for i, zed_info in enumerate(zed_list):
        serial_number = str(zed_info.serial_number)
        camera_id = f"ZED_{serial_number}"
        print(f"  Camera {i}: {camera_id}")
        
        # Test creating ZedCamera instance
        try:
            from devices.zed_camera import ZedCamera
            camera = ZedCamera(camera_id=camera_id, serial_number=serial_number)
            print(f"    ✓ ZedCamera instance created successfully")
            
            # Test connection
            try:
                camera.connect()
                print(f"    ✓ Connected successfully")
                print(f"    ✓ Is connected: {camera.is_connected}")
                
                # Test frame capture
                try:
                    frame = camera.capture_frame()
                    print(f"    ✓ Frame captured: {frame.camera_id}")
                    print(f"    ✓ RGB shape: {frame.rgb_image.shape}")
                    print(f"    ✓ Depth shape: {frame.depth_image.shape}")
                except Exception as e:
                    print(f"    ✗ Frame capture failed: {e}")
                
                camera.disconnect()
                print(f"    ✓ Disconnected successfully")
                
            except Exception as e:
                print(f"    ✗ Connection failed: {e}")
                
        except Exception as e:
            print(f"    ✗ ZedCamera creation failed: {e}")
            import traceback
            traceback.print_exc()
    
    if len(zed_list) == 0:
        print("No ZED cameras found. Make sure your ZED camera is connected.")
        
except ImportError as e:
    print(f"Failed to import ZED SDK: {e}")
    print("Make sure the ZED SDK is properly installed.")
except Exception as e:
    print(f"Unexpected error: {e}")
    import traceback
    traceback.print_exc()
