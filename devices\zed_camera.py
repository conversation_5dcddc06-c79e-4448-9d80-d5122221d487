import pyzed.sl as sl
import numpy as np
import time
from typing import Iterator

from devices.abstract_camera import AbstractCamera
from models.camera import Frame

class ZedCamera(AbstractCamera):
    """Concrete implementation of AbstractCamera for ZED cameras."""

    def __init__(self, camera_id: str, serial_number: str):
        self._camera_id = camera_id
        self._serial_number = serial_number
        self._zed = sl.Camera()
        self._init_params = sl.InitParameters()
        self._is_connected = False
        self._sequence_id = 0
        self._calibration_status = None

        # Configure initialization parameters
        self._init_params.camera_resolution = sl.RESOLUTION.HD720
        self._init_params.camera_fps = 30
        self._init_params.depth_mode = sl.DEPTH_MODE.PERFORMANCE
        self._init_params.coordinate_units = sl.UNIT.MILLIMETER
        self._init_params.depth_stabilization = True
        self._init_params.depth_minimum_distance = 200

        # Disable self-calibration to avoid the warning
        # This can be re-enabled later if needed
        self._init_params.enable_self_calib = False

        self._init_params.set_from_serial_number(int(self._serial_number))

    def connect(self) -> None:
        """Connect to the ZED camera with enhanced error handling."""
        print(f"Connecting to ZED camera {self._camera_id}...")

        # First attempt: Connect with self-calibration disabled
        status = self._zed.open(self._init_params)

        if status == sl.ERROR_CODE.SUCCESS:
            self._is_connected = True
            print(f"ZED camera {self._camera_id} connected successfully.")

            # Check calibration status
            calibration_status = self._zed.get_camera_information().calibration_parameters_raw
            if calibration_status is not None:
                print(f"ZED camera {self._camera_id}: Calibration data available.")
                self._calibration_status = "available"
            else:
                print(f"ZED camera {self._camera_id}: Using factory calibration.")
                self._calibration_status = "factory"

        elif status == sl.ERROR_CODE.CAMERA_NOT_DETECTED:
            self._is_connected = False
            raise ConnectionError(f"ZED Camera {self._camera_id} not detected. Check USB connection.")

        elif status == sl.ERROR_CODE.CAMERA_DETECTION_ISSUE:
            self._is_connected = False
            raise ConnectionError(f"ZED Camera {self._camera_id} detection issue. Try reconnecting the camera.")

        elif status == sl.ERROR_CODE.INVALID_FUNCTION_PARAMETERS:
            self._is_connected = False
            raise ConnectionError(f"ZED Camera {self._camera_id} invalid parameters. Check serial number.")

        else:
            self._is_connected = False
            print(f"Error connecting to ZED camera {self._camera_id}: {status}")
            raise ConnectionError(f"ZED Camera connection error: {status}")

    def enable_self_calibration(self) -> bool:
        """
        Enable self-calibration for better depth accuracy.
        Call this method after connecting if you want to perform self-calibration.
        Returns True if calibration was successful, False otherwise.
        """
        if not self._is_connected:
            print(f"Cannot calibrate: ZED camera {self._camera_id} is not connected.")
            return False

        print(f"Starting self-calibration for ZED camera {self._camera_id}...")
        print("Please point the camera towards a textured area with good lighting.")
        print("Avoid objects closer than 1 meter during calibration.")

        # Disconnect and reconnect with self-calibration enabled
        self._zed.close()
        self._init_params.enable_self_calib = True

        status = self._zed.open(self._init_params)
        if status == sl.ERROR_CODE.SUCCESS:
            self._calibration_status = "self_calibrated"
            print(f"ZED camera {self._camera_id}: Self-calibration completed successfully.")
            return True
        else:
            # If self-calibration fails, fall back to disabled mode
            print(f"ZED camera {self._camera_id}: Self-calibration failed. Using factory calibration.")
            self._init_params.enable_self_calib = False
            status = self._zed.open(self._init_params)
            if status == sl.ERROR_CODE.SUCCESS:
                self._calibration_status = "factory"
                return False
            else:
                self._is_connected = False
                raise ConnectionError(f"ZED Camera failed to reconnect after calibration attempt: {status}")

    @property
    def calibration_status(self) -> str:
        """Get the current calibration status."""
        return self._calibration_status or "unknown"

    def get_calibration_guidance(self) -> str:
        """Get user guidance for improving calibration."""
        if self._calibration_status == "factory":
            return (
                "ZED camera is using factory calibration. For better depth accuracy:\n"
                "1. Point the camera towards a textured area (books, posters, etc.)\n"
                "2. Ensure good lighting (avoid dark areas)\n"
                "3. Keep objects at least 1 meter away\n"
                "4. Call enable_self_calibration() method to recalibrate"
            )
        elif self._calibration_status == "self_calibrated":
            return "ZED camera is using self-calibration. Depth accuracy is optimized."
        else:
            return "ZED camera calibration status unknown."

    def disconnect(self) -> None:
        if self._is_connected:
            self._zed.close()
            self._is_connected = False
            print(f"ZED camera {self._camera_id} disconnected.")

    def capture_frame(self) -> Frame:
        if not self._is_connected:
            raise ConnectionError(f"Camera {self._camera_id} is not connected.")

        runtime_params = sl.RuntimeParameters()
        if self._zed.grab(runtime_params) == sl.ERROR_CODE.SUCCESS:
            image_sl = sl.Mat()
            depth_sl = sl.Mat()
            self._zed.retrieve_image(image_sl, sl.VIEW.LEFT)
            self._zed.retrieve_measure(depth_sl, sl.MEASURE.DEPTH)

            rgb_image = image_sl.get_data()[:, :, :3]
            depth_image = depth_sl.get_data()

            timestamp_ns = int(time.time_ns())
            frame = Frame(
                camera_id=self._camera_id,
                sequence_id=self._sequence_id,
                timestamp_ns=timestamp_ns,
                rgb_image=rgb_image,
                depth_image=depth_image
            )
            self._sequence_id += 1
            return frame
        else:
            raise RuntimeError("Failed to grab ZED frame.")

    def stream(self) -> Iterator[Frame]:
        while self._is_connected:
            yield self.capture_frame()

    @property
    def is_connected(self) -> bool:
        return self._is_connected

    @property
    def camera_id(self) -> str:
        return self._camera_id

