#!/usr/bin/env python3
"""
Test script to verify ZED camera frame capture and display.
"""

import sys
import os
import cv2
import numpy as np

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import pyzed.sl as sl
    from devices.zed_camera import ZedCamera
    
    print("Testing ZED camera frame capture...")
    
    # Discover ZED cameras
    zed_list = sl.Camera.get_device_list()
    if len(zed_list) == 0:
        print("No ZED cameras found!")
        sys.exit(1)
    
    # Use the first ZED camera found
    zed_info = zed_list[0]
    serial_number = str(zed_info.serial_number)
    camera_id = f"ZED_{serial_number}"
    
    print(f"Testing camera: {camera_id}")
    
    # Create and connect camera
    camera = ZedCamera(camera_id=camera_id, serial_number=serial_number)
    camera.connect()
    
    if not camera.is_connected:
        print("Failed to connect to camera!")
        sys.exit(1)
    
    print("Camera connected successfully!")
    
    # Capture a few frames to test
    for i in range(5):
        try:
            print(f"Capturing frame {i+1}...")
            frame = camera.capture_frame()
            
            print(f"  Frame {i+1} captured successfully:")
            print(f"    Camera ID: {frame.camera_id}")
            print(f"    Sequence ID: {frame.sequence_id}")
            print(f"    RGB shape: {frame.rgb_image.shape}")
            print(f"    RGB dtype: {frame.rgb_image.dtype}")
            print(f"    RGB min/max: {frame.rgb_image.min()}/{frame.rgb_image.max()}")
            print(f"    Depth shape: {frame.depth_image.shape}")
            print(f"    Depth dtype: {frame.depth_image.dtype}")
            
            # Check if RGB image has valid data
            if frame.rgb_image.size > 0:
                # Save a test image
                test_filename = f"test_frame_{i+1}.jpg"
                # Convert BGR to RGB for saving
                rgb_for_save = cv2.cvtColor(frame.rgb_image, cv2.COLOR_BGR2RGB)
                cv2.imwrite(test_filename, rgb_for_save)
                print(f"    Saved test image: {test_filename}")
            else:
                print(f"    WARNING: RGB image is empty!")
                
        except Exception as e:
            print(f"  Error capturing frame {i+1}: {e}")
            import traceback
            traceback.print_exc()
    
    # Disconnect camera
    camera.disconnect()
    print("Camera disconnected.")
    print("\nTest completed! Check the saved test images to verify camera output.")
    
except ImportError as e:
    print(f"Import error: {e}")
except Exception as e:
    print(f"Unexpected error: {e}")
    import traceback
    traceback.print_exc()
